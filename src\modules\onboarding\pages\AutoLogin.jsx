import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { Spinner } from '@heroui/react';
import { autoLoginUser } from '../../../core/redux/slices/authSlice';
import { RouteNames } from '../../../core/routes/routes';

function AutoLogin() {
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const { token } = useParams();
    const [checking, setChecking] = useState(true);

    useEffect(() => {
        const handleAutoLogin = async () => {

            try {
                setChecking(true);

                // Decode the token if it's URL encoded
                const decodedToken = decodeURIComponent(token);

                // Dispatch the auto-login action
                const resultAction = await dispatch(autoLoginUser(decodedToken));


                // Check if the action was fulfilled
                if (autoLoginUser.fulfilled.match(resultAction)) {
                    // Redirect to dashboard on success
                    navigate(RouteNames.dashboard, { replace: true });
                } else {
                    console.log('res', resultAction);
                    // If auto-login failed, redirect to 404
                    navigate(RouteNames.notfound, { replace: true });
                }
            } catch (error) {
                console.error('Auto-login error:', error);
                // On any error, redirect to 404
                navigate(RouteNames.notfound, { replace: true });
            } finally {
                setChecking(false);
            }
        };

        // Only proceed if token exists
        if (token) {
            handleAutoLogin();
        } else {
            // No token provided, redirect to 404
            navigate(RouteNames.notfound, { replace: true });
        }
    }, [token, navigate, dispatch]);

    // Show loading spinner while checking
    return (
        <div className="w-screen h-screen bg-base_light dark:bg-dark-gradient flex flex-col items-center justify-center">
            <div className="text-center">
                <Spinner size="lg" />
                <p className="mt-4 text-gray-600 dark:text-gray-400">
                    Authenticating...
                </p>
            </div>
        </div>
    );
}

export default AutoLogin;
