import {
    BrowserRouter as Router,
    Routes,
    Route,
    Outlet,
    Navigate,
    useLocation,
} from 'react-router-dom';
import { RouteNames, RoutesConfig } from "./routes";
import LeadsSources from "../../modules/lead-sources/pages/LeadsSources";
import SourcingRequests from "../../modules/sourcing-request/pages/SourcingRequests";
import OrderManagement from "../../modules/order-management/pages/OrderManagement";
import Stock from "../../modules/stock/pages/Stock";
import ServiceInvoices from "../../modules/invoices/pages/ServiceInvoices";
import Funds from "../../modules/statistics/pages/Funds";
import Analytics from "../../modules/statistics/pages/Analytics";
import Notifications from "../../modules/notifications/pages/Notifications";
import Settings from "../../modules/settings/pages/Settings";
import ReferralsSection from "../../modules/referral/pages/Referral";
import HelpSection from "../../modules/help/pages/Help";
import Login from "../../modules/onboarding/pages/Login";
import ForgotPassword from "../../modules/onboarding/pages/ForgotPassword";
import ChangePassword from "../../modules/onboarding/pages/ChangePassword";
import RecoverPasswordEmailSent from "../../modules/onboarding/pages/RecoverPasswordEmailSent";
import Dashboard from "../../modules/dashboard/pages/Dashboard";
import Signup from "../../modules/onboarding/pages/Signup";
import Sidebar from "../../modules/dashboard/components/partials/Sidebar.jsx";
import ResideBar from '../../modules/dashboard/components/partials/ResideBar.jsx';
import SourcingInvoices from '../../modules/invoices/pages/SourcingInvoices.jsx';
import AuthGuard from './AuthGuard.jsx';
import FollowUp from '../../modules/order-management/pages/FollowUp.jsx';
import Simulator from '../../modules/tools/pages/Simulator.jsx';
import ProductStatics from '../../modules/statistics/pages/ProductStatics.jsx';
import NotFoundPage from './404.jsx';
import RedirectToLeadSources from '../../modules/lead-sources/pages/RedirectToLeadSources.jsx';
import AutoLogin from '../../modules/onboarding/pages/AutoLogin.jsx';

const routes = [
    {
        path: "/",
        element: <Navigate to={RouteNames.dashboard} replace />
    },
    {
        path: RouteNames.dashboard,
        element: <Dashboard />,
        requiresAuth: true,
    },
    {
        path: RouteNames.notification,
        element: <Notifications />,
        requiresAuth: true,
    },
    {
        path: RouteNames.settings,
        element: <Settings />,
        requiresAuth: true,
    },
    {
        path: RouteNames.help,
        element: <HelpSection />,
        requiresAuth: true,
    },
    {
        path: RouteNames.referrals,
        element: <ReferralsSection />,
        requiresAuth: true,
    },
    {
        path: RouteNames.login,
        element: <Login />,
        requiresAuth: false,
    },
    {
        path: RouteNames.signup,
        element: <Signup />,
        requiresAuth: false,
    },
    {
        path: RouteNames.forgotPassword,
        element: <ForgotPassword />,
        requiresAuth: false,
    },
    {
        path: RouteNames.changePassword,
        element: <ChangePassword />,
        requiresAuth: false,
    },
    {
        path: RouteNames.recoverEmailSent,
        element: <RecoverPasswordEmailSent />,
        requiresAuth: false,
    },
    {
        path: RouteNames.SourcingRequest,
        element: <SourcingRequests />,
        requiresAuth: true,
    },
    {
        path: RouteNames.leadSources,
        element: <LeadsSources />,
        requiresAuth: true,
    },
    {
        path: RouteNames.allOrders,
        element: <OrderManagement />,
        requiresAuth: true,
    },
    {
        path: RouteNames.followUp,
        element: <FollowUp />,
        requiresAuth: true,
        requiredPermission: 'sellers.followuporders',
    },
    {
        path: RouteNames.allProducts,
        element: <Stock />,
        requiresAuth: true,
    },
    {
        path: RouteNames.serviceInvoices,
        element: <ServiceInvoices />,
        requiresAuth: true,
    },
    {
        path: RouteNames.sourcingInvoices,
        element: <SourcingInvoices />,
        requiresAuth: true,
    },
    {
        path: RouteNames.Funds,
        element: <Funds />,
        requiresAuth: true,
    },
    {
        path: RouteNames.productStatistics,
        element: <ProductStatics />,
        requiresAuth: true,
    },
    {
        path: RouteNames.analytics,
        element: <Analytics />,
        requiresAuth: true,
    },
    {
        path: RouteNames.simulator,
        element: <Simulator />,
        requiresAuth: true,
    },

    {
        path: RouteNames.notfound,
        element: <NotFoundPage />,
        requiresAuth: false,
    },
    {
        path: "/dropify/auth/authorize",
        element: <RedirectToLeadSources />,
        requiresAuth: false,
    },
    {
        path: "/autologin/:token",
        element: <AutoLogin />,
        requiresAuth: false,
    },
    {
        path: "*",
        element: <NotFoundPage />,
        requiresAuth: false,
    },
];

// Define the paths for onboarding pages
const onboardingRoutes = [
    RouteNames.login,
    RouteNames.signup,
    RouteNames.forgotPassword,
    RouteNames.changePassword,
    RouteNames.recoverEmailSent,
];

// Define paths that should not show sidebar (including 404 and dropify auth)
const noSidebarRoutes = [
    ...onboardingRoutes,
    RouteNames.notfound,
    "/dropify/auth/authorize",
    "/autologin",
    "*" // catch-all route
];
// Define the paths for onboarding pages
const defaultSet = [
    RouteNames.settings,
    RouteNames.referrals,
    RouteNames.help,
    RouteNames.notification,
];

function LayoutWrapper({ children }) {
    const location = useLocation();

    // Check if the current route should not show sidebar (onboarding, 404, etc.)
    const isNoSidebarPage = noSidebarRoutes.some(
        (path) => {
            if (path === "*") {
                // For catch-all route, check if current path matches any defined route
                const definedPaths = [
                    RouteNames.dashboard,
                    RouteNames.notification,
                    RouteNames.settings,
                    RouteNames.help,
                    RouteNames.referrals,
                    RouteNames.login,
                    RouteNames.signup,
                    RouteNames.forgotPassword,
                    RouteNames.changePassword,
                    RouteNames.recoverEmailSent,
                    RouteNames.SourcingRequest,
                    RouteNames.leadSources,
                    RouteNames.allOrders,
                    RouteNames.followUp,
                    RouteNames.allProducts,
                    RouteNames.serviceInvoices,
                    RouteNames.sourcingInvoices,
                    RouteNames.Funds,
                    RouteNames.productStatistics,
                    RouteNames.analytics,
                    RouteNames.simulator,
                    RouteNames.notfound,
                    "/dropify/auth/authorize"
                ];
                return !definedPaths.includes(location.pathname) && !location.pathname.startsWith('/autologin/');
            }
            if (path === "/autologin") {
                // Handle auto-login route with parameter
                return location.pathname.startsWith('/autologin/');
            }
            return location.pathname === path;
        }
    );

    // Render a full-screen layout for pages without sidebar
    if (isNoSidebarPage) {
        return (
            <div className="w-screen h-screen bg-base_light dark:bg-dark-gradient">
                {children}
            </div>
        );
    }

    return (
        <div className="w-screen h-screen bg-base_light dark:bg-dark-gradient">
            <div className="max-w-[3000px] mx-auto w-full flex h-full relative overflow-hidden">
                <ResideBar />
                <Sidebar />
                {children}
            </div>
        </div>
    );
}

export default function RoutersWrapper() {
    function generateRoutes(routes) {
        return routes.map((route, index) => {

            const { children, ...rest } = route;

            const shouldHideRoute = !defaultSet.includes(route.path) && !onboardingRoutes.includes(route.path) && RoutesConfig.some(config => {
                if (config.path === route.path && config.showInSidebar === false) {
                    return true;
                }

                if (config.children) {
                    return config.children.some(child =>
                        child.path === route.path && child.showInSidebar === false
                    );
                }

                return false;
            });

            if (shouldHideRoute) {
                return (
                    <Route
                        key={index}
                        path={rest.path}
                        element={<Navigate to={RouteNames.dashboard} replace />}
                    />
                );
            }

            const element = route.element ? (
                <AuthGuard requiresAuth={route.requiresAuth} requiredPermission={route.requiredPermission}>
                    {route.element}
                </AuthGuard>
            ) : (
                <AuthGuard requiresAuth={route.requiresAuth} requiredPermission={route.requiredPermission}>
                    <Outlet />
                </AuthGuard>
            );

            return (
                <Route key={index} path={rest.path} element={element}>
                    {children && generateRoutes(children)}
                </Route>
            );
        }).filter(route => route !== null); // Filter out null routes
    }



    return (
        <Router>
            <LayoutWrapper>

                <Routes>{generateRoutes(routes)}</Routes>
            </LayoutWrapper>
        </Router>
    );
}
